<template>
	<page-container :isShowNav="false" bgColorPage="#E2ECEE">
		<image
			:src="backgroundImage"
			:style="backgroundImageStyle"
			mode="aspectFill"
			class="w-100% h-360 fixed top-0 z-0"
		/>
		<custom-nav bg-color="unset" title="" :is-back="true"> </custom-nav>
		<view class="content-wrapper px-24">
			<view class="server-info sticky top-200">
				<view class="relative flex justify-between">
					<view class="flex flex-col">
						<view class="text-36 font-bold">宝塔面板-AAA-BBB</view>
						<view class="text-24 py-16">IP：************* | Opencloudos 9</view>
						<view class="text-24 flex items-center">
							<view class="w-16 h-16 bg-#20a50a rd-50%"></view>
							<text class="text-24 pl-16">持续运行3天</text>
						</view>
					</view>
					<image
						src="@/static/index/server-bg.png"
						mode="aspectFit"
						class="absolute -top-120 -right-80 w-460 h-460"
					></image>
				</view>
			</view>
			<view class="detail mt-68">
				<function-list
					title="功能"
					:function-list="basicFunctionList"
					:show-edit="true"
					:columns="5"
					@itemClick="() => {}"
					@editClick="() => {}"
				/>
				<function-list
					class="mt-24"
					title="插件"
					:function-list="pluginFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="() => {}"
					@editClick="() => {}"
				/>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
	import { onReady, onUnload, onShow, onHide, onLoad } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import BtButton from '@/components/BtButton/index.vue';
	import { useConfigStore } from '@/store/modules/config';
	import ECharts from '@/components/ECharts/index.vue';
	import { $t } from '@/locale/index.js';
	import bgLight from '@/static/index/bg-light.png';
	import bgDark from '@/static/index/bg-dark.png';
	import FunctionList from './functionList.vue';

	const basicFunctionList = ref([
		{
			label: '网站',
			icon: 'home',
		},
		{
			label: '数据库',
			icon: 'home',
		},
		{
			label: '系统防火墙',
			icon: 'home',
		},
		{
			label: '终端',
			icon: 'home',
		},
		{
			label: '安全风险',
			icon: 'home',
		},
		{
			label: '监控',
			icon: 'home',
		},
		{
			label: 'SSH管理',
			icon: 'home',
		},
		{
			label: '文件管理',
			icon: 'home',
		},
		{
			label: '计划任务',
			icon: 'home',
		},
		{
			label: '日志',
			icon: 'home',
		}
	]);

	const pluginFunctionList = ref([
		{
			label: '插件',
			icon: 'home',
		},
	]);

	const backgroundImage = ref(bgLight);
	const backgroundImageStyle = ref({
		backgroundImage: `url(${backgroundImage.value})`,
		backgroundSize: 'cover',
		backgroundPosition: 'center',
	});
</script>
<style lang="scss" scoped></style>
